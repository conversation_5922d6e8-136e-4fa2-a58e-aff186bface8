import React from 'react';
import { Button, Box } from '@mui/material';

interface GooglePhotosButtonProps {
  onClick: () => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const GooglePhotosButton: React.FC<GooglePhotosButtonProps> = ({
  onClick,
  disabled = false,
  size = 'medium',
  fullWidth = false
}) => {
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          height: 36,
          fontSize: '0.875rem',
          padding: '8px 16px',
          iconSize: 16
        };
      case 'large':
        return {
          height: 48,
          fontSize: '1rem',
          padding: '12px 24px',
          iconSize: 20
        };
      default: // medium
        return {
          height: 40,
          fontSize: '0.875rem',
          padding: '10px 20px',
          iconSize: 18
        };
    }
  };

  const sizeStyles = getSizeStyles();

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      fullWidth={fullWidth}
      sx={{
        height: sizeStyles.height,
        fontSize: sizeStyles.fontSize,
        padding: sizeStyles.padding,
        backgroundColor: '#1a73e8',
        color: 'white',
        textTransform: 'none',
        fontWeight: 500,
        fontFamily: 'Google Sans, Roboto, Arial, sans-serif',
        borderRadius: '4px',
        border: 'none',
        boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)',
        transition: 'all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1)',
        '&:hover': {
          backgroundColor: '#1557b0',
          boxShadow: '0 1px 3px 0 rgba(60,64,67,.3), 0 4px 8px 3px rgba(60,64,67,.15)',
        },
        '&:active': {
          backgroundColor: '#1246a0',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)',
        },
        '&:focus': {
          outline: 'none',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15), 0 0 0 2px rgba(26,115,232,.24)',
        },
        '&:disabled': {
          backgroundColor: '#f1f3f4',
          color: '#5f6368',
          boxShadow: 'none',
          cursor: 'not-allowed',
        },
        minWidth: 'auto',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}
    >
      <Box
        component="svg"
        sx={{
          width: sizeStyles.iconSize,
          height: sizeStyles.iconSize,
          flexShrink: 0
        }}
        viewBox="0 0 24 24"
        fill="none"
      >
        {/* Google Photos icon - multicolored pinwheel */}
        <path d="M12 2l3.09 6.26L22 9l-5.91 3.74L17.82 19 12 16l-5.82 3 1.73-6.26L2 9l6.91-.74L12 2z" fill="#4285F4"/>
        <path d="M12 2v14l-5.82 3 1.73-6.26L2 9l6.91-.74L12 2z" fill="#34A853"/>
        <path d="M12 16l5.82 3-1.73-6.26L22 9l-6.91-.74L12 2v14z" fill="#FBBC05"/>
        <path d="M12 2l3.09 6.26L22 9l-5.91 3.74L17.82 19 12 16V2z" fill="#EA4335"/>
      </Box>
      Load from Google Photos
    </Button>
  );
};

export default GooglePhotosButton;
