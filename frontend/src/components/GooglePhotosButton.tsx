import React from 'react';
import { Button, Box } from '@mui/material';

interface GooglePhotosButtonProps {
  onClick: () => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const GooglePhotosButton: React.FC<GooglePhotosButtonProps> = ({
  onClick,
  disabled = false,
  size = 'medium',
  fullWidth = false
}) => {
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          height: 36,
          fontSize: '0.875rem',
          padding: '8px 16px',
          iconSize: 16
        };
      case 'large':
        return {
          height: 48,
          fontSize: '1rem',
          padding: '12px 24px',
          iconSize: 20
        };
      default: // medium
        return {
          height: 40,
          fontSize: '0.875rem',
          padding: '10px 20px',
          iconSize: 18
        };
    }
  };

  const sizeStyles = getSizeStyles();

  return (
    <Button
      variant="contained"
      onClick={onClick}
      disabled={disabled}
      fullWidth={fullWidth}
      sx={{
        height: sizeStyles.height,
        fontSize: sizeStyles.fontSize,
        padding: sizeStyles.padding,
        backgroundColor: 'white !important',
        color: 'text.primary',
        textTransform: 'none',
        fontWeight: 500,
        borderRadius: 1,
        boxShadow: 'none',
        '&:hover': {
          backgroundColor: 'white !important',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)'
        },
        '&:disabled': {
          backgroundColor: 'white !important',
          color: 'action.disabled',
          boxShadow: 'none',
        },
        minWidth: 'auto',
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}
    >
      <Box
        component="img"
        src="/Google_Photos_icon_(2020).svg"
        alt="Google Photos"
        sx={{
          width: sizeStyles.iconSize,
          height: sizeStyles.iconSize,
          flexShrink: 0
        }}
      />
      Load from Google Photos
    </Button>
  );
};

export default GooglePhotosButton;
