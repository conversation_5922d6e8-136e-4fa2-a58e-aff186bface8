import React from 'react';
import { Button, Box } from '@mui/material';

interface GooglePhotosButtonProps {
  onClick: () => void;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const GooglePhotosButton: React.FC<GooglePhotosButtonProps> = ({
  onClick,
  disabled = false,
  size = 'medium',
  fullWidth = false
}) => {
  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          height: 36,
          fontSize: '0.875rem',
          padding: '8px 16px',
          iconSize: 16
        };
      case 'large':
        return {
          height: 48,
          fontSize: '1rem',
          padding: '12px 24px',
          iconSize: 20
        };
      default: // medium
        return {
          height: 40,
          fontSize: '0.875rem',
          padding: '10px 20px',
          iconSize: 18
        };
    }
  };

  const sizeStyles = getSizeStyles();

  return (
    <Button
      onClick={onClick}
      disabled={disabled}
      fullWidth={fullWidth}
      sx={{
        height: sizeStyles.height,
        fontSize: sizeStyles.fontSize,
        padding: sizeStyles.padding,
        backgroundColor: 'white',
        color: '#5f6368',
        textTransform: 'none',
        fontWeight: 500,
        fontFamily: 'Google Sans, Roboto, Arial, sans-serif',
        borderRadius: '8px',
        border: '1px solid #dadce0',
        boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)',
        transition: 'all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1)',
        '&:hover': {
          backgroundColor: '#f8f9fa',
          boxShadow: '0 1px 3px 0 rgba(60,64,67,.3), 0 4px 8px 3px rgba(60,64,67,.15)',
          borderColor: '#dadce0',
        },
        '&:active': {
          backgroundColor: '#f1f3f4',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15)',
        },
        '&:focus': {
          outline: 'none',
          boxShadow: '0 1px 2px 0 rgba(60,64,67,.3), 0 1px 3px 1px rgba(60,64,67,.15), 0 0 0 2px rgba(26,115,232,.24)',
        },
        '&:disabled': {
          backgroundColor: '#f1f3f4',
          color: '#9aa0a6',
          boxShadow: 'none',
          cursor: 'not-allowed',
          borderColor: '#f1f3f4',
        },
        minWidth: 'auto',
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}
    >
      <Box
        component="img"
        src="/Google_Photos_icon_(2020).svg"
        alt="Google Photos"
        sx={{
          width: sizeStyles.iconSize,
          height: sizeStyles.iconSize,
          flexShrink: 0
        }}
      />
      Load from Google Photos
    </Button>
  );
};

export default GooglePhotosButton;
