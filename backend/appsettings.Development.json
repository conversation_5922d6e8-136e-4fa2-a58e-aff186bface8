{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Host=localhost;Port=5432;Database=vidcompressor;Username=vidcompressor;Password=password"}, "Google": {"ClientId": "546390650743-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com", "RedirectUri": "http://localhost:3000/auth/callback"}, "GoogleCloud": {"ProjectId": "tranquil-bison-465923-v9", "Region": "us-central1", "InputBucketName": "tranquil-bison-465923-v9-vidcompressor-input-dev", "OutputBucketName": "tranquil-bison-465923-v9-vidcompressor-output-dev", "TempBucketName": "tranquil-bison-465923-v9-vidcompressor-temp-dev", "Transcoder": {"Location": "us-central1"}, "CloudTasks": {"ProjectId": "tranquil-bison-465923-v9", "Location": "us-central1", "QueueName": "video-compression-jobs-dev", "HandlerUrl": "https://9275277fb1ec.ngrok-free.app"}}, "Stripe": {"SecretKey": "sk_test_51RnA4DPs90kiajQ5ekTQPoLJmGcstZFsaNsDVpvQvtsdZiylQy7O4eWNhiuZJ5XS85xxxqTmdh5LqPZE2bPCzWML00qeoWbYAv", "WebhookSecret": "YOUR_STRIPE_WEBHOOK_SECRET"}, "Jwt": {"Secret": "THIS IS A SUPER SECRET KEY THAT SHOULD BE REPLACED"}}