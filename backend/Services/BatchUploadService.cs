using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.SignalR;
using VidCompressor.Models;
using VidCompressor.Services;
using VidCompressor.Hubs;

namespace VidCompressor.Services;

public class BatchUploadService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<BatchUploadService> _logger;
    private readonly IHubContext<NotificationHub> _hubContext;
    private readonly TimeSpan _batchInterval;

    public BatchUploadService(
        IServiceProvider serviceProvider,
        ILogger<BatchUploadService> logger,
        IHubContext<NotificationHub> hubContext)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _hubContext = hubContext;
        _batchInterval = TimeSpan.FromSeconds(30); // Batch upload every 30 seconds
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Batch upload service started. Processing batches every {Interval} seconds", _batchInterval.TotalSeconds);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await ProcessBatchUploadsAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in batch upload processing");
            }

            await Task.Delay(_batchInterval, stoppingToken);
        }
    }

    private async Task ProcessBatchUploadsAsync(CancellationToken cancellationToken)
    {
        using var scope = _serviceProvider.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var googlePhotosService = scope.ServiceProvider.GetRequiredService<GooglePhotosService>();

        // Get jobs ready for batch upload, grouped by user
        var readyJobs = await context.CompressionJobs
            .Where(j => j.Status == CompressionJobStatus.ReadyForBatchUpload && 
                       j.UploadToGooglePhotos && 
                       !string.IsNullOrEmpty(j.CompressedFilePath))
            .GroupBy(j => j.UserId)
            .ToListAsync(cancellationToken);

        if (!readyJobs.Any())
        {
            return; // No jobs ready for upload
        }

        _logger.LogInformation("Found {UserCount} users with jobs ready for batch upload", readyJobs.Count);

        foreach (var userGroup in readyJobs)
        {
            var userId = userGroup.Key;
            var userJobs = userGroup.ToList();

            _logger.LogInformation("Processing batch upload for user {UserId} with {JobCount} jobs", userId, userJobs.Count);

            try
            {
                await ProcessUserBatchUploadAsync(context, googlePhotosService, userId, userJobs, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process batch upload for user {UserId}", userId);
                
                // Mark all jobs in this batch as failed
                foreach (var job in userJobs)
                {
                    await MarkJobAsFailed(context, job, $"Batch upload failed: {ex.Message}");
                }
            }
        }

        await context.SaveChangesAsync(cancellationToken);
    }

    private async Task ProcessUserBatchUploadAsync(
        ApplicationDbContext context,
        GooglePhotosService googlePhotosService,
        string userId,
        List<CompressionJob> jobs,
        CancellationToken cancellationToken)
    {
        // Get user's access token
        var accessToken = await GetUserAccessToken(context, userId);
        if (string.IsNullOrEmpty(accessToken))
        {
            _logger.LogError("Unable to get access token for user {UserId}", userId);
            foreach (var job in jobs)
            {
                await MarkJobAsFailed(context, job, "Unable to get valid access token");
            }
            return;
        }

        // Update all jobs to uploading status
        foreach (var job in jobs)
        {
            job.Status = CompressionJobStatus.UploadingToGooglePhotos;
            await SendStatusUpdate(job, "Uploading to Google Photos");
        }
        await context.SaveChangesAsync(cancellationToken);

        try
        {
            // Convert CompressionJob to BatchUploadItem
            var batchItems = jobs.Select(job => new VidCompressor.Services.BatchUploadItem
            {
                Id = job.Id,
                CompressedFilePath = job.CompressedFilePath ?? string.Empty,
                MediaType = (int)job.MediaType // Convert enum to int
            }).ToList();

            // Perform batch upload
            await googlePhotosService.BatchUploadAsync(accessToken, batchItems, cancellationToken);

            // Mark all jobs as completed (or move to next step if needed)
            foreach (var job in jobs)
            {
                if (job.OverwriteOriginal)
                {
                    job.Status = CompressionJobStatus.DeletingOriginal;
                    await SendStatusUpdate(job, "Deleting original media");
                    // TODO: Implement original deletion logic
                }
                else
                {
                    job.Status = CompressionJobStatus.Completed;
                    job.CompletedAt = DateTime.UtcNow;
                    await SendStatusUpdate(job, "Compression completed successfully");
                }

                // Clean up temporary file
                if (!string.IsNullOrEmpty(job.CompressedFilePath) && File.Exists(job.CompressedFilePath))
                {
                    try
                    {
                        File.Delete(job.CompressedFilePath);
                        job.CompressedFilePath = null;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to delete temporary file {FilePath} for job {JobId}", 
                            job.CompressedFilePath, job.Id);
                    }
                }
            }

            _logger.LogInformation("Successfully completed batch upload for user {UserId} with {JobCount} jobs", 
                userId, jobs.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Batch upload failed for user {UserId}", userId);
            foreach (var job in jobs)
            {
                await MarkJobAsFailed(context, job, $"Batch upload failed: {ex.Message}");
            }
        }
    }

    private async Task<string?> GetUserAccessToken(ApplicationDbContext context, string userId)
    {
        var user = await context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        if (user?.GoogleRefreshToken == null)
        {
            return null;
        }

        // TODO: Implement token refresh logic if needed
        // For now, assume GoogleAccessToken is still valid
        return user.GoogleAccessToken;
    }

    private async Task MarkJobAsFailed(ApplicationDbContext context, CompressionJob job, string errorMessage)
    {
        job.Status = CompressionJobStatus.Failed;
        job.ErrorMessage = errorMessage;
        job.CompletedAt = DateTime.UtcNow;

        await SendStatusUpdate(job, $"Failed: {errorMessage}");

        // Clean up temporary file
        if (!string.IsNullOrEmpty(job.CompressedFilePath) && File.Exists(job.CompressedFilePath))
        {
            try
            {
                File.Delete(job.CompressedFilePath);
                job.CompressedFilePath = null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to delete temporary file {FilePath} for failed job {JobId}", 
                    job.CompressedFilePath, job.Id);
            }
        }
    }

    private async Task SendStatusUpdate(CompressionJob job, string message)
    {
        try
        {
            await _hubContext.Clients.All.SendAsync("CompressionStatusUpdate", new
            {
                jobId = job.Id,
                mediaItemId = job.MediaItemId,
                status = job.Status.ToString(),
                message = message,
                progress = GetProgressPercentage(job.Status, job.MediaType),
                userId = job.UserId,
                completedAt = job.CompletedAt,
                compressionRatio = job.CompressionRatio
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send status update for job {JobId}", job.Id);
        }
    }

    private static int GetProgressPercentage(CompressionJobStatus status, MediaType mediaType)
    {
        // Different progress flows for photos vs videos
        if (mediaType == MediaType.Photo)
        {
            return status switch
            {
                CompressionJobStatus.Queued => 0,
                CompressionJobStatus.DownloadingFromGooglePhotos => 20,
                // These shouldn't happen for photos, but if they do, maintain reasonable progress
                CompressionJobStatus.UploadingToStorage => 30,
                CompressionJobStatus.CompressingImage => 50,
                CompressionJobStatus.DownloadingFromStorage => 60,
                CompressionJobStatus.ReadyForBatchUpload => 70,
                CompressionJobStatus.UploadingToGooglePhotos => 90,
                CompressionJobStatus.DeletingOriginal => 95,
                CompressionJobStatus.Completed => 100,
                CompressionJobStatus.Failed => 0,
                CompressionJobStatus.Cancelled => 0,
                // For any unrecognized status, return 50% to avoid going backward
                _ => 50
            };
        }
        else // Video
        {
            return status switch
            {
                CompressionJobStatus.Queued => 0,
                CompressionJobStatus.DownloadingFromGooglePhotos => 10,
                CompressionJobStatus.UploadingToStorage => 20,
                CompressionJobStatus.TranscodingInProgress => 40,
                CompressionJobStatus.CompressingImage => 40, // Shouldn't happen for videos, but just in case
                CompressionJobStatus.DownloadingFromStorage => 60,
                CompressionJobStatus.ReadyForBatchUpload => 70,
                CompressionJobStatus.UploadingToGooglePhotos => 90,
                CompressionJobStatus.DeletingOriginal => 95,
                CompressionJobStatus.Completed => 100,
                CompressionJobStatus.Failed => 0,
                CompressionJobStatus.Cancelled => 0,
                // For any unrecognized status, return 50% to avoid going backward
                _ => 50
            };
        }
    }
}
