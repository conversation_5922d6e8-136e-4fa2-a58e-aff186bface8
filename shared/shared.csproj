<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="CasCap.Api.GooglePhotos" Version="3.1.0" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="4.10.0" />
    <PackageReference Include="Google.Cloud.Video.Transcoder.V1" Version="2.6.0" />
    <PackageReference Include="Google.Cloud.Tasks.V2" Version="3.5.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
    <PackageReference Include="Polly" Version="8.6.2" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.7" />
  </ItemGroup>

</Project>
